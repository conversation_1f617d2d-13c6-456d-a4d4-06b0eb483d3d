//----------------------------------------------
//            Realistic Car Controller
//
// Copyright © 2014 - 2024 BoneCracker Games
// https://www.bonecrackergames.com
// <PERSON><PERSON><PERSON>
//
// Enhanced Trailer Stability System
// Develop by <PERSON>
//----------------------------------------------

using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(RCC_TruckTrailer))]
public class RCC_TruckTrailerEditor : Editor {

    private RCC_TruckTrailer trailer;
    private GUIStyle headerStyle;
    private GUIStyle brandingStyle;

    private void OnEnable() {
        trailer = (RCC_TruckTrailer)target;
    }

    public override void OnInspectorGUI() {
        
        serializedObject.Update();

        // Initialize styles
        if (headerStyle == null) {
            headerStyle = new GUIStyle(EditorStyles.boldLabel);
            headerStyle.fontSize = 14;
            headerStyle.normal.textColor = new Color(0.2f, 0.6f, 1f);
        }

        if (brandingStyle == null) {
            brandingStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel);
            brandingStyle.fontSize = 11;
            brandingStyle.fontStyle = FontStyle.Italic;
            brandingStyle.normal.textColor = new Color(0.7f, 0.7f, 0.7f);
        }

        // Branding Header
        EditorGUILayout.Space(10);
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("🚛 Enhanced Trailer Stability System", brandingStyle);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("Develop by Ali Taj", brandingStyle);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        EditorGUILayout.Space(10);

        // Stability Settings Section
        EditorGUILayout.LabelField("🎯 Stability Settings", headerStyle);
        EditorGUILayout.Space(5);
        
        EditorGUI.BeginChangeCheck();
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("stabilityMultiplier"), 
            new GUIContent("Stability Multiplier", "Higher values provide more stability at high speeds"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("speedBasedStiffness"), 
            new GUIContent("Speed Based Stiffness", "How much friction increases with speed"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("maxStabilitySpeed"), 
            new GUIContent("Max Stability Speed (km/h)", "Speed at which maximum stability is applied"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("angularDragMultiplier"), 
            new GUIContent("Angular Drag Multiplier", "Additional rotational damping for stability"));

        EditorGUILayout.Space(10);

        // Wheel Configuration Section
        EditorGUILayout.LabelField("🛞 Wheel Configuration", headerStyle);
        EditorGUILayout.Space(5);
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("trailerWheels"), true);

        EditorGUILayout.Space(10);

        // Physics Settings Section
        EditorGUILayout.LabelField("⚙️ Physics Settings", headerStyle);
        EditorGUILayout.Space(5);
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("COM"), 
            new GUIContent("Center of Mass", "Center of mass transform"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("legs"), 
            new GUIContent("Support Legs", "Legs GameObject (enabled when detached)"));

        EditorGUILayout.Space(10);

        // Brake Settings Section
        EditorGUILayout.LabelField("🛑 Brake Settings", headerStyle);
        EditorGUILayout.Space(5);
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("brakeWhenDetached"), 
            new GUIContent("Brake When Detached", "Apply brakes automatically when trailer is detached"));
        
        if (trailer.brakeWhenDetached) {
            EditorGUI.indentLevel++;
            EditorGUILayout.PropertyField(serializedObject.FindProperty("brakeForce"), 
                new GUIContent("Brake Force", "Force applied to brakes when detached"));
            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space(10);

        // Status Information
        EditorGUILayout.LabelField("📊 Status Information", headerStyle);
        EditorGUILayout.Space(5);
        
        EditorGUI.BeginDisabledGroup(true);
        EditorGUILayout.Toggle("Attached", trailer.attached);
        
        if (Application.isPlaying && trailer.GetComponent<Rigidbody>()) {
            float speed = trailer.GetComponent<Rigidbody>().linearVelocity.magnitude * 3.6f;
            EditorGUILayout.FloatField("Current Speed (km/h)", speed);
        }
        EditorGUI.EndDisabledGroup();

        if (EditorGUI.EndChangeCheck()) {
            serializedObject.ApplyModifiedProperties();
        }

        EditorGUILayout.Space(15);

        // Help Box
        EditorGUILayout.HelpBox(
            "Enhanced Trailer Stability System:\n" +
            "• Stability Multiplier: Increases grip at high speeds\n" +
            "• Speed Based Stiffness: Controls how much stability increases with speed\n" +
            "• Max Stability Speed: Speed threshold for maximum stability effect\n" +
            "• Angular Drag Multiplier: Reduces unwanted rotation during turns\n\n" +
            "Recommended settings for better high-speed stability:\n" +
            "• Stability Multiplier: 1.2-1.5\n" +
            "• Speed Based Stiffness: 0.6-0.8\n" +
            "• Max Stability Speed: 80-100 km/h\n" +
            "• Angular Drag Multiplier: 1.2-1.8", 
            MessageType.Info);

        EditorGUILayout.Space(10);

        // Footer Branding
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("Enhanced by Ali Taj for Better Trailer Physics", brandingStyle);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
    }
}
