using UnityEngine;
using UnityEditor;

/// <summary>
/// Custom Editor for Trailer Load Compensator
/// Develop by <PERSON>
/// </summary>
[CustomEditor(typeof(TrailerLoadCompensator))]
public class TrailerLoadCompensatorEditor : Editor 
{
    private TrailerLoadCompensator compensator;
    private GUIStyle headerStyle;
    private GUIStyle brandingStyle;
    private GUIStyle sectionStyle;

    private void OnEnable() 
    {
        compensator = (TrailerLoadCompensator)target;
    }

    public override void OnInspectorGUI() 
    {
        serializedObject.Update();

        // Initialize styles
        InitializeStyles();

        // Main branding header
        DrawBrandingHeader();

        EditorGUILayout.Space(10);

        // Power Compensation Settings
        DrawPowerSettings();

        EditorGUILayout.Space(10);

        // Advanced Settings
        DrawAdvancedSettings();

        EditorGUILayout.Space(10);

        // Real-time Status
        DrawStatusInformation();

        EditorGUILayout.Space(10);

        // Control Buttons
        DrawControlButtons();

        EditorGUILayout.Space(10);

        // Help Section
        DrawHelpSection();

        EditorGUILayout.Space(10);

        // Footer branding
        DrawFooterBranding();

        serializedObject.ApplyModifiedProperties();
    }

    private void InitializeStyles() 
    {
        if (headerStyle == null) 
        {
            headerStyle = new GUIStyle(EditorStyles.boldLabel);
            headerStyle.fontSize = 14;
            headerStyle.normal.textColor = new Color(0.2f, 0.7f, 1f);
        }

        if (brandingStyle == null) 
        {
            brandingStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel);
            brandingStyle.fontSize = 12;
            brandingStyle.fontStyle = FontStyle.Bold;
            brandingStyle.normal.textColor = new Color(0.1f, 0.6f, 0.9f);
        }

        if (sectionStyle == null) 
        {
            sectionStyle = new GUIStyle(EditorStyles.helpBox);
            sectionStyle.padding = new RectOffset(10, 10, 5, 5);
        }
    }

    private void DrawBrandingHeader() 
    {
        EditorGUILayout.BeginVertical(sectionStyle);
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("⚡ TRAILER LOAD COMPENSATOR", brandingStyle);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("Automatic Power Adjustment for Heavy Loads", EditorStyles.centeredGreyMiniLabel);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("Develop by Ali Taj", EditorStyles.centeredGreyMiniLabel);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
    }

    private void DrawPowerSettings() 
    {
        EditorGUILayout.LabelField("⚡ Power Compensation Settings", headerStyle);
        EditorGUILayout.BeginVertical(sectionStyle);
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("maxTorqueMultiplier"), 
            new GUIContent("Max Torque Multiplier", "Maximum torque increase (Recommended: 2.0-2.5)"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("maxEngineRPMMultiplier"), 
            new GUIContent("Max Engine RPM Multiplier", "Maximum RPM increase (Recommended: 1.3-1.5)"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("gearRatioMultiplier"), 
            new GUIContent("Gear Ratio Multiplier", "Gear ratio adjustment for pulling power (Recommended: 1.2-1.4)"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("baseTrailerMass"), 
            new GUIContent("Base Trailer Mass (kg)", "Reference mass for compensation calculation"));
        
        EditorGUILayout.EndVertical();
    }

    private void DrawAdvancedSettings() 
    {
        EditorGUILayout.LabelField("🔧 Advanced Settings", headerStyle);
        EditorGUILayout.BeginVertical(sectionStyle);
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("compensationSmoothness"), 
            new GUIContent("Compensation Smoothness", "How smoothly power adjusts (Lower = Smoother)"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("massThreshold"), 
            new GUIContent("Mass Threshold (kg)", "Mass above which compensation starts"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("maxCompensationFactor"), 
            new GUIContent("Max Compensation Factor", "Maximum overall compensation multiplier"));
        
        EditorGUILayout.EndVertical();
    }

    private void DrawStatusInformation() 
    {
        EditorGUILayout.LabelField("📊 Real-time Status", headerStyle);
        EditorGUILayout.BeginVertical(sectionStyle);
        
        EditorGUI.BeginDisabledGroup(true);
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("currentTotalMass"), 
            new GUIContent("Total Mass (kg)", "Current total mass including trailers"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("currentCompensationFactor"), 
            new GUIContent("Compensation Factor", "Current power multiplication factor"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("attachedTrailerCount"), 
            new GUIContent("Attached Trailers", "Number of trailers detected"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("compensationActive"), 
            new GUIContent("Compensation Active", "Whether compensation is currently applied"));
        
        EditorGUI.EndDisabledGroup();
        
        if (Application.isPlaying) 
        {
            if (compensator.GetComponent<SerializedProperty>().FindPropertyRelative("compensationActive").boolValue) 
            {
                EditorGUILayout.HelpBox("✅ Compensation is ACTIVE - Truck power increased for heavy load", MessageType.Info);
            }
            else 
            {
                EditorGUILayout.HelpBox("⚪ Compensation is INACTIVE - Normal truck power", MessageType.None);
            }
        }
        
        EditorGUILayout.EndVertical();
    }

    private void DrawControlButtons() 
    {
        EditorGUILayout.LabelField("🎮 Controls", headerStyle);
        EditorGUILayout.BeginVertical(sectionStyle);
        
        if (Application.isPlaying) 
        {
            if (GUILayout.Button("🔄 Force Recalculation", GUILayout.Height(30))) 
            {
                compensator.ForceRecalculation();
                EditorUtility.DisplayDialog("Recalculation", "Trailer load compensation has been recalculated!", "OK");
            }
        }
        else 
        {
            EditorGUILayout.HelpBox("Controls are available during Play Mode", MessageType.Info);
        }
        
        EditorGUILayout.EndVertical();
    }

    private void DrawHelpSection() 
    {
        EditorGUILayout.HelpBox(
            "⚡ TRAILER LOAD COMPENSATOR FEATURES:\n\n" +
            "✅ Automatic power adjustment based on trailer load\n" +
            "✅ Detects RCC trailers and standalone trailers\n" +
            "✅ Handles trailer-to-trailer connections\n" +
            "✅ Smooth power transitions\n" +
            "✅ Mass and count-based compensation\n" +
            "✅ Preserves original truck settings\n\n" +
            "HOW IT WORKS:\n" +
            "• Detects all connected trailers automatically\n" +
            "• Calculates total mass and trailer count\n" +
            "• Increases torque, RPM, and gear ratios accordingly\n" +
            "• Smoothly transitions between power levels\n\n" +
            "RECOMMENDED SETTINGS:\n" +
            "• Max Torque Multiplier: 2.0-2.5\n" +
            "• Max Engine RPM Multiplier: 1.3-1.5\n" +
            "• Gear Ratio Multiplier: 1.2-1.4\n" +
            "• Base Trailer Mass: 3000kg\n\n" +
            "TROUBLESHOOTING:\n" +
            "• If truck still struggles: Increase Max Torque Multiplier\n" +
            "• If power is too much: Decrease Max Compensation Factor\n" +
            "• If transitions are jerky: Increase Compensation Smoothness", 
            MessageType.Info);
    }

    private void DrawFooterBranding() 
    {
        EditorGUILayout.BeginVertical(sectionStyle);
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("🚛 Smart Load Management System", EditorStyles.centeredGreyMiniLabel);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("Develop by Ali Taj - Intelligent Truck Solutions", EditorStyles.centeredGreyMiniLabel);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
    }
}
