using UnityEngine;
using UnityEditor;

/// <summary>
/// Custom Editor for Standalone Trailer Controller
/// Develop by <PERSON>
/// </summary>
[CustomEditor(typeof(StandaloneTrailerController))]
public class StandaloneTrailerControllerEditor : Editor
{
    private StandaloneTrailerController trailer;
    private GUIStyle headerStyle;
    private GUIStyle brandingStyle;
    private GUIStyle sectionStyle;

    private void OnEnable()
    {
        trailer = (StandaloneTrailerController)target;
    }

    public override void OnInspectorGUI() 
    {
        serializedObject.Update();

        // Initialize styles
        InitializeStyles();

        // Main branding header
        DrawBrandingHeader();

        EditorGUILayout.Space(10);

        // Stability Settings
        DrawStabilitySettings();

        EditorGUILayout.Space(10);

        // Wheel Configuration
        DrawWheelConfiguration();

        EditorGUILayout.Space(10);

        // Physics Settings
        DrawPhysicsSettings();

        EditorGUILayout.Space(10);

        // Connection Settings
        DrawConnectionSettings();

        EditorGUILayout.Space(10);

        // Brake Settings
        DrawBrakeSettings();

        EditorGUILayout.Space(10);

        // Status Information
        DrawStatusInformation();

        EditorGUILayout.Space(10);

        // Help and Instructions
        DrawHelpSection();

        EditorGUILayout.Space(10);

        // Footer branding
        DrawFooterBranding();

        serializedObject.ApplyModifiedProperties();
    }

    private void InitializeStyles() 
    {
        if (headerStyle == null) 
        {
            headerStyle = new GUIStyle(EditorStyles.boldLabel);
            headerStyle.fontSize = 14;
            headerStyle.normal.textColor = new Color(0.2f, 0.7f, 1f);
        }

        if (brandingStyle == null) 
        {
            brandingStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel);
            brandingStyle.fontSize = 12;
            brandingStyle.fontStyle = FontStyle.Bold;
            brandingStyle.normal.textColor = new Color(0.1f, 0.6f, 0.9f);
        }

        if (sectionStyle == null) 
        {
            sectionStyle = new GUIStyle(EditorStyles.helpBox);
            sectionStyle.padding = new RectOffset(10, 10, 5, 5);
        }
    }

    private void DrawBrandingHeader() 
    {
        EditorGUILayout.BeginVertical(sectionStyle);
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("🚛 STANDALONE TRAILER SYSTEM", brandingStyle);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("Independent Physics & Stability Control", EditorStyles.centeredGreyMiniLabel);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("Develop by Ali Taj", EditorStyles.centeredGreyMiniLabel);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
    }

    private void DrawStabilitySettings() 
    {
        EditorGUILayout.LabelField("🎯 Stability Settings", headerStyle);
        EditorGUILayout.BeginVertical(sectionStyle);
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("stabilityMultiplier"), 
            new GUIContent("Stability Multiplier", "Higher values = more stability at high speeds (Recommended: 1.2-1.5)"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("speedBasedStiffness"), 
            new GUIContent("Speed Based Stiffness", "How much friction increases with speed (Recommended: 0.6-0.8)"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("maxStabilitySpeed"), 
            new GUIContent("Max Stability Speed (km/h)", "Speed at which maximum stability is applied (Recommended: 80-100)"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("angularDragMultiplier"), 
            new GUIContent("Angular Drag Multiplier", "Additional rotational damping for stability (Recommended: 1.5-2.0)"));
        
        EditorGUILayout.EndVertical();
    }

    private void DrawWheelConfiguration() 
    {
        EditorGUILayout.LabelField("🛞 Wheel Configuration", headerStyle);
        EditorGUILayout.BeginVertical(sectionStyle);
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("trailerWheels"), true);
        
        EditorGUILayout.EndVertical();
    }

    private void DrawPhysicsSettings() 
    {
        EditorGUILayout.LabelField("⚙️ Physics Settings", headerStyle);
        EditorGUILayout.BeginVertical(sectionStyle);
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("centerOfMass"), 
            new GUIContent("Center of Mass", "Transform representing the center of mass"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("supportLegs"), 
            new GUIContent("Support Legs", "GameObject activated when trailer is detached"));
        
        EditorGUILayout.EndVertical();
    }

    private void DrawConnectionSettings() 
    {
        EditorGUILayout.LabelField("🔗 Connection Settings", headerStyle);
        EditorGUILayout.BeginVertical(sectionStyle);
        
        EditorGUI.BeginDisabledGroup(true);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("isAttached"), 
            new GUIContent("Is Attached", "Current attachment status"));
        EditorGUI.EndDisabledGroup();
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("attachmentDistance"), 
            new GUIContent("Attachment Distance", "Auto-attachment detection range"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("attachableLayerMask"), 
            new GUIContent("Attachable Layer Mask", "Layers that can be attached to"));
        
        EditorGUILayout.EndVertical();
    }

    private void DrawBrakeSettings() 
    {
        EditorGUILayout.LabelField("🛑 Brake Settings", headerStyle);
        EditorGUILayout.BeginVertical(sectionStyle);
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("brakeWhenDetached"), 
            new GUIContent("Brake When Detached", "Automatically apply brakes when not attached"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("brakeForce"), 
            new GUIContent("Brake Force", "Force applied when detached"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("handbrakeForce"), 
            new GUIContent("Handbrake Force", "Force applied when handbrake is used"));
        
        EditorGUILayout.EndVertical();
    }



    private void DrawStatusInformation() 
    {
        EditorGUILayout.LabelField("📊 Status Information", headerStyle);
        EditorGUILayout.BeginVertical(sectionStyle);
        
        EditorGUI.BeginDisabledGroup(true);
        
        EditorGUILayout.Toggle("Attached", trailer.isAttached);
        
        if (Application.isPlaying && trailer.GetComponent<Rigidbody>()) 
        {
            float speed = trailer.GetComponent<Rigidbody>().linearVelocity.magnitude * 3.6f;
            EditorGUILayout.FloatField("Current Speed (km/h)", speed);
            
            if (trailer.isAttached) 
            {
                EditorGUILayout.LabelField("Status", "✅ Connected & Stable");
            }
            else 
            {
                EditorGUILayout.LabelField("Status", "⚠️ Detached - Looking for connection");
            }
        }
        
        EditorGUI.EndDisabledGroup();
        
        EditorGUILayout.EndVertical();
    }

    private void DrawHelpSection() 
    {
        EditorGUILayout.HelpBox(
            "🚛 STANDALONE TRAILER SYSTEM FEATURES:\n\n" +
            "✅ Independent of RCC system\n" +
            "✅ Trailer-to-trailer connections\n" +
            "✅ Advanced stability control\n" +
            "✅ Speed-based friction adjustment\n" +
            "✅ Anti-sway technology\n" +
            "✅ Auto-attachment system\n" +
            "✅ Configurable physics\n\n" +
            "USAGE:\n" +
            "• Use AttachToTarget() method to connect trailers\n" +
            "• Use DetachTrailer() method to disconnect\n" +
            "• Auto-attachment works when trailers are nearby\n\n" +
            "RECOMMENDED SETTINGS:\n" +
            "• Stability Multiplier: 1.3\n" +
            "• Speed Based Stiffness: 0.7\n" +
            "• Max Stability Speed: 90 km/h\n" +
            "• Angular Drag Multiplier: 1.8",
            MessageType.Info);
    }

    private void DrawFooterBranding() 
    {
        EditorGUILayout.BeginVertical(sectionStyle);
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("🔧 Enhanced Trailer Physics System", EditorStyles.centeredGreyMiniLabel);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("Develop by Ali Taj - Independent Trailer Solutions", EditorStyles.centeredGreyMiniLabel);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
    }
}
