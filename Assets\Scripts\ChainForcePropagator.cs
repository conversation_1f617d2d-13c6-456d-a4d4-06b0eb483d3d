using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Chain Force Propagator - Ensures force flows through entire trailer chain
/// Develop by <PERSON>
/// </summary>
public class ChainForcePropagator : MonoBehaviour 
{
    [Header("⛓️ Chain Force Propagator - Develop by <PERSON>")]
    [Space(10)]

    [Header("🔗 Chain Detection")]
    [Range(1f, 10f)] public float chainDetectionRange = 5f;
    [Range(0.1f, 2f)] public float updateInterval = 0.5f;
    public LayerMask chainLayerMask = -1;

    [Header("⚡ Force Propagation")]
    [Range(0.5f, 2f)] public float forcePropagationEfficiency = 0.9f;
    [Range(100f, 10000f)] public float maxPropagationForce = 3000f;
    [Range(0.1f, 5f)] public float forceAmplification = 1.5f;
    [Range(0.1f, 2f)] public float dampingFactor = 0.7f;

    [Header("🎯 Force Application")]
    [Range(0.1f, 3f)] public float forceApplicationPoint = 1f;
    [Range(0.1f, 1f)] public float forceSpreadRadius = 0.5f;
    [Range(10f, 1000f)] public float minimumForceThreshold = 100f;

    [Header("📊 Chain Status")]
    [SerializeField] private bool isChainHead = false;
    [SerializeField] private bool isChainTail = false;
    [SerializeField] private int chainPosition = 0;
    [SerializeField] private int totalChainLength = 0;
    [SerializeField] private float currentPropagatedForce = 0f;

    // Private variables
    private List<ChainForcePropagator> chainMembers = new List<ChainForcePropagator>();
    private ChainForcePropagator previousInChain;
    private ChainForcePropagator nextInChain;
    private RCC_CarControllerV4 sourceTruck;
    private Rigidbody rigid;
    private ConfigurableJoint joint;
    private float lastUpdateTime = 0f;
    private Vector3 lastForceDirection = Vector3.forward;

    private void Start() 
    {
        rigid = GetComponent<Rigidbody>();
        joint = GetComponent<ConfigurableJoint>();
        
        // Delay chain analysis to ensure all objects are initialized
        Invoke(nameof(AnalyzeChain), 1.5f);
        
        Debug.Log($"⛓️ Chain Force Propagator initialized on: {gameObject.name}");
    }

    private void FixedUpdate() 
    {
        // Update chain analysis periodically
        if (Time.time - lastUpdateTime > updateInterval) 
        {
            AnalyzeChain();
            lastUpdateTime = Time.time;
        }

        // Propagate force through chain
        PropagateForce();
    }

    private void AnalyzeChain() 
    {
        chainMembers.Clear();
        previousInChain = null;
        nextInChain = null;
        sourceTruck = null;
        
        // Find all chain members
        FindChainMembers();
        
        // Determine our position in chain
        DetermineChainPosition();
        
        // Find source truck
        FindSourceTruck();
        
        Debug.Log($"⛓️ Chain analysis: Position {chainPosition}/{totalChainLength}, Head: {isChainHead}, Tail: {isChainTail}");
    }

    private void FindChainMembers() 
    {
        // Use breadth-first search to find all connected chain members
        Queue<ChainForcePropagator> searchQueue = new Queue<ChainForcePropagator>();
        HashSet<ChainForcePropagator> visited = new HashSet<ChainForcePropagator>();
        
        searchQueue.Enqueue(this);
        visited.Add(this);
        
        while (searchQueue.Count > 0) 
        {
            ChainForcePropagator current = searchQueue.Dequeue();
            chainMembers.Add(current);
            
            // Find connected objects
            Collider[] nearbyColliders = Physics.OverlapSphere(current.transform.position, chainDetectionRange, chainLayerMask);
            
            foreach (Collider col in nearbyColliders) 
            {
                ChainForcePropagator otherMember = col.GetComponent<ChainForcePropagator>();
                if (otherMember && !visited.Contains(otherMember)) 
                {
                    // Check if they're actually connected via joint
                    if (AreConnectedByJoint(current, otherMember)) 
                    {
                        searchQueue.Enqueue(otherMember);
                        visited.Add(otherMember);
                    }
                }
            }
        }
        
        totalChainLength = chainMembers.Count;
    }

    private bool AreConnectedByJoint(ChainForcePropagator member1, ChainForcePropagator member2) 
    {
        ConfigurableJoint joint1 = member1.GetComponent<ConfigurableJoint>();
        ConfigurableJoint joint2 = member2.GetComponent<ConfigurableJoint>();
        
        if (joint1 && joint1.connectedBody == member2.GetComponent<Rigidbody>()) 
            return true;
            
        if (joint2 && joint2.connectedBody == member1.GetComponent<Rigidbody>()) 
            return true;
            
        return false;
    }

    private void DetermineChainPosition() 
    {
        // Find our direct connections
        foreach (ChainForcePropagator member in chainMembers) 
        {
            if (member == this) continue;
            
            ConfigurableJoint memberJoint = member.GetComponent<ConfigurableJoint>();
            
            // Check if this member is connected to us
            if (memberJoint && memberJoint.connectedBody == rigid) 
            {
                nextInChain = member;
            }
            
            // Check if we're connected to this member
            if (joint && joint.connectedBody == member.GetComponent<Rigidbody>()) 
            {
                previousInChain = member;
            }
        }
        
        // Determine position in chain
        if (previousInChain == null) 
        {
            isChainHead = true;
            chainPosition = 1;
        }
        else if (nextInChain == null) 
        {
            isChainTail = true;
            chainPosition = totalChainLength;
        }
        else 
        {
            isChainHead = false;
            isChainTail = false;
            // Calculate position by counting from head
            chainPosition = CalculatePositionFromHead();
        }
    }

    private int CalculatePositionFromHead() 
    {
        int position = 1;
        ChainForcePropagator current = this;
        
        // Walk backwards to find head
        while (current.previousInChain != null) 
        {
            current = current.previousInChain;
            position++;
        }
        
        return totalChainLength - position + 1;
    }

    private void FindSourceTruck() 
    {
        // Check if we're directly connected to a truck
        if (joint && joint.connectedBody) 
        {
            sourceTruck = joint.connectedBody.GetComponent<RCC_CarControllerV4>();
        }
        
        // If not found, check previous chain member
        if (!sourceTruck && previousInChain) 
        {
            sourceTruck = previousInChain.sourceTruck;
        }
        
        // If still not found, search all chain members
        if (!sourceTruck) 
        {
            foreach (ChainForcePropagator member in chainMembers) 
            {
                ConfigurableJoint memberJoint = member.GetComponent<ConfigurableJoint>();
                if (memberJoint && memberJoint.connectedBody) 
                {
                    RCC_CarControllerV4 truck = memberJoint.connectedBody.GetComponent<RCC_CarControllerV4>();
                    if (truck) 
                    {
                        sourceTruck = truck;
                        break;
                    }
                }
            }
        }
    }

    private void PropagateForce() 
    {
        if (!sourceTruck || isChainHead) return;

        // Calculate force to apply based on truck's power and our position in chain
        Vector3 propagatedForce = CalculatePropagatedForce();
        
        if (propagatedForce.magnitude > minimumForceThreshold) 
        {
            ApplyPropagatedForce(propagatedForce);
            currentPropagatedForce = propagatedForce.magnitude;
        }
        else 
        {
            currentPropagatedForce = 0f;
        }
    }

    private Vector3 CalculatePropagatedForce() 
    {
        if (!sourceTruck) return Vector3.zero;

        // Base force from truck
        float truckThrottle = sourceTruck.throttleInput;
        float truckTorque = sourceTruck.engineTorque;
        Vector3 truckDirection = sourceTruck.transform.forward;
        
        // Calculate base force
        float baseForce = truckTorque * truckThrottle * forceAmplification;
        
        // Apply efficiency loss based on chain position
        float efficiencyLoss = Mathf.Pow(forcePropagationEfficiency, chainPosition - 1);
        float adjustedForce = baseForce * efficiencyLoss;
        
        // Apply damping
        adjustedForce *= dampingFactor;
        
        // Clamp to maximum
        adjustedForce = Mathf.Min(adjustedForce, maxPropagationForce);
        
        // Determine force direction
        Vector3 forceDirection = truckDirection;
        if (previousInChain) 
        {
            // Use direction from previous chain member to us
            forceDirection = (transform.position - previousInChain.transform.position).normalized;
        }
        
        lastForceDirection = forceDirection;
        return forceDirection * adjustedForce;
    }

    private void ApplyPropagatedForce(Vector3 force) 
    {
        if (!rigid) return;

        // Apply force at multiple points for better distribution
        Vector3 basePosition = transform.position + Vector3.up * forceApplicationPoint;
        
        // Main force application
        rigid.AddForceAtPosition(force, basePosition, ForceMode.Force);
        
        // Additional force points for better stability
        Vector3 leftPoint = basePosition + transform.right * forceSpreadRadius;
        Vector3 rightPoint = basePosition - transform.right * forceSpreadRadius;
        
        rigid.AddForceAtPosition(force * 0.3f, leftPoint, ForceMode.Force);
        rigid.AddForceAtPosition(force * 0.3f, rightPoint, ForceMode.Force);
        
        // Visual debug
        Debug.DrawRay(basePosition, force.normalized * 3f, Color.red, 0.1f);
        
        Debug.Log($"⛓️ Applied {force.magnitude:F1}N force to chain position {chainPosition}: {gameObject.name}");
    }

    [ContextMenu("Analyze Chain")]
    public void ForceAnalyzeChain() 
    {
        AnalyzeChain();
    }

    private void OnDrawGizmosSelected() 
    {
        // Draw chain detection range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, chainDetectionRange);
        
        // Draw chain connections
        if (previousInChain) 
        {
            Gizmos.color = Color.green;
            Gizmos.DrawLine(transform.position, previousInChain.transform.position);
        }
        
        if (nextInChain) 
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(transform.position, nextInChain.transform.position);
        }
        
        // Draw connection to source truck
        if (sourceTruck) 
        {
            Gizmos.color = Color.magenta;
            Gizmos.DrawLine(transform.position, sourceTruck.transform.position);
        }
        
        // Draw force direction
        if (lastForceDirection != Vector3.zero) 
        {
            Gizmos.color = Color.red;
            Vector3 forcePos = transform.position + Vector3.up * forceApplicationPoint;
            Gizmos.DrawRay(forcePos, lastForceDirection * 2f);
        }
    }

    private void OnDrawGizmos() 
    {
        // Show chain position
        if (isChainHead) 
        {
            Gizmos.color = Color.green;
            Gizmos.DrawWireCube(transform.position + Vector3.up * 3f, Vector3.one * 0.8f);
        }
        else if (isChainTail) 
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireCube(transform.position + Vector3.up * 3f, Vector3.one * 0.6f);
        }
        else 
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireCube(transform.position + Vector3.up * 3f, Vector3.one * 0.4f);
        }
    }
}
