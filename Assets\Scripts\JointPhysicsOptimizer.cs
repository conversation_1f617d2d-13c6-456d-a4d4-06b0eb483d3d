using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Joint Physics Optimizer - Fixes joint and friction issues for heavy trailer loads
/// Develop by <PERSON>
/// </summary>
public class JointPhysicsOptimizer : MonoBehaviour 
{
    [Header("🔧 Joint Physics Optimizer - Develop by <PERSON>")]
    [Space(10)]

    [Header("⚙️ Joint Optimization Settings")]
    [Range(10000f, 100000f)] public float jointMaxForce = 50000f;
    [Range(1000f, 20000f)] public float jointDamping = 8000f;
    [Range(100f, 5000f)] public float jointSpring = 2000f;
    [Range(0.1f, 2f)] public float jointBreakForceMultiplier = 1.5f;

    [Header("🛞 Wheel Friction Enhancement")]
    [Range(1f, 3f)] public float forwardStiffnessMultiplier = 2f;
    [Range(1f, 3f)] public float sidewaysStiffnessMultiplier = 2.5f;
    [Range(0.1f, 0.8f)] public float extremumSlipReduction = 0.3f;
    [Range(0.1f, 0.8f)] public float asymptoteSlipReduction = 0.4f;

    [Header("🚛 Truck Wheel Optimization")]
    [Range(1f, 5f)] public float truckWheelGripMultiplier = 2.5f;
    [Range(50f, 200f)] public float wheelMassMultiplier = 100f;
    [Range(0.1f, 2f)] public float wheelDampingRate = 1.5f;

    [Header("📊 Debug Information")]
    [SerializeField] private int optimizedJoints = 0;
    [SerializeField] private int optimizedWheels = 0;
    [SerializeField] private bool optimizationActive = false;

    private List<ConfigurableJoint> processedJoints = new List<ConfigurableJoint>();
    private List<WheelCollider> processedWheels = new List<WheelCollider>();
    private Dictionary<WheelCollider, WheelFrictionCurve> originalForwardFrictions = new Dictionary<WheelCollider, WheelFrictionCurve>();
    private Dictionary<WheelCollider, WheelFrictionCurve> originalSidewaysFrictions = new Dictionary<WheelCollider, WheelFrictionCurve>();

    private void Start() 
    {
        // Delay optimization to ensure all objects are initialized
        Invoke(nameof(OptimizePhysics), 1f);
    }

    [ContextMenu("Optimize Physics")]
    public void OptimizePhysics() 
    {
        Debug.Log("🔧 Starting Joint Physics Optimization...");
        
        optimizedJoints = 0;
        optimizedWheels = 0;
        optimizationActive = true;

        // Find and optimize all joints in the scene
        OptimizeAllJoints();
        
        // Find and optimize all wheel colliders
        OptimizeAllWheelColliders();
        
        // Optimize truck wheels specifically
        OptimizeTruckWheels();

        Debug.Log($"✅ Optimization Complete! Joints: {optimizedJoints}, Wheels: {optimizedWheels}");
    }

    private void OptimizeAllJoints() 
    {
        // Find all ConfigurableJoints in the scene
        ConfigurableJoint[] allJoints = FindObjectsOfType<ConfigurableJoint>();
        
        foreach (ConfigurableJoint joint in allJoints) 
        {
            if (!processedJoints.Contains(joint)) 
            {
                OptimizeJoint(joint);
                processedJoints.Add(joint);
                optimizedJoints++;
            }
        }
    }

    private void OptimizeJoint(ConfigurableJoint joint) 
    {
        if (!joint) return;

        // Store original break force
        float originalBreakForce = joint.breakForce;
        
        // Optimize joint drives for better force transmission
        JointDrive linearDrive = joint.xDrive;
        linearDrive.positionSpring = jointSpring;
        linearDrive.positionDamper = jointDamping;
        linearDrive.maximumForce = jointMaxForce;
        joint.xDrive = linearDrive;
        joint.yDrive = linearDrive;
        joint.zDrive = linearDrive;

        // Optimize angular drives
        JointDrive angularDrive = joint.angularXDrive;
        angularDrive.positionSpring = jointSpring * 0.8f;
        angularDrive.positionDamper = jointDamping * 0.6f;
        angularDrive.maximumForce = jointMaxForce * 0.7f;
        joint.angularXDrive = angularDrive;
        joint.angularYZDrive = angularDrive;

        // Increase break force to prevent unwanted disconnections
        if (originalBreakForce != Mathf.Infinity) 
        {
            joint.breakForce = originalBreakForce * jointBreakForceMultiplier;
            joint.breakTorque = joint.breakTorque * jointBreakForceMultiplier;
        }

        // Ensure proper configuration
        joint.configuredInWorldSpace = true;
        joint.enableCollision = false;
        joint.enablePreprocessing = true;

        Debug.Log($"🔧 Optimized joint on: {joint.name}");
    }

    private void OptimizeAllWheelColliders() 
    {
        // Find all WheelColliders in the scene
        WheelCollider[] allWheels = FindObjectsOfType<WheelCollider>();
        
        foreach (WheelCollider wheel in allWheels) 
        {
            if (!processedWheels.Contains(wheel)) 
            {
                OptimizeWheelCollider(wheel);
                processedWheels.Add(wheel);
                optimizedWheels++;
            }
        }
    }

    private void OptimizeWheelCollider(WheelCollider wheel) 
    {
        if (!wheel) return;

        // Store original friction curves
        if (!originalForwardFrictions.ContainsKey(wheel)) 
        {
            originalForwardFrictions[wheel] = wheel.forwardFriction;
            originalSidewaysFrictions[wheel] = wheel.sidewaysFriction;
        }

        // Optimize forward friction for better traction
        WheelFrictionCurve forwardFriction = wheel.forwardFriction;
        forwardFriction.stiffness *= forwardStiffnessMultiplier;
        forwardFriction.extremumSlip = Mathf.Max(0.1f, forwardFriction.extremumSlip - extremumSlipReduction);
        forwardFriction.asymptoteSlip = Mathf.Max(0.2f, forwardFriction.asymptoteSlip - asymptoteSlipReduction);
        forwardFriction.extremumValue = Mathf.Min(2f, forwardFriction.extremumValue * 1.2f);
        forwardFriction.asymptoteValue = Mathf.Min(1.5f, forwardFriction.asymptoteValue * 1.1f);
        wheel.forwardFriction = forwardFriction;

        // Optimize sideways friction for better stability
        WheelFrictionCurve sidewaysFriction = wheel.sidewaysFriction;
        sidewaysFriction.stiffness *= sidewaysStiffnessMultiplier;
        sidewaysFriction.extremumSlip = Mathf.Max(0.05f, sidewaysFriction.extremumSlip - extremumSlipReduction);
        sidewaysFriction.asymptoteSlip = Mathf.Max(0.1f, sidewaysFriction.asymptoteSlip - asymptoteSlipReduction);
        sidewaysFriction.extremumValue = Mathf.Min(2f, sidewaysFriction.extremumValue * 1.3f);
        sidewaysFriction.asymptoteValue = Mathf.Min(1.5f, sidewaysFriction.asymptoteValue * 1.2f);
        wheel.sidewaysFriction = sidewaysFriction;

        // Optimize wheel mass and damping
        wheel.mass = wheelMassMultiplier;
        wheel.wheelDampingRate = wheelDampingRate;

        // Optimize suspension for heavy loads
        JointSpring suspension = wheel.suspensionSpring;
        suspension.spring = Mathf.Max(suspension.spring, 40000f);
        suspension.damper = Mathf.Max(suspension.damper, 3000f);
        wheel.suspensionSpring = suspension;

        Debug.Log($"🛞 Optimized wheel: {wheel.name}");
    }

    private void OptimizeTruckWheels() 
    {
        // Find RCC truck controllers
        RCC_CarControllerV4[] trucks = FindObjectsOfType<RCC_CarControllerV4>();
        
        foreach (RCC_CarControllerV4 truck in trucks) 
        {
            // Check if truck has attached trailers
            if (truck.attachedTrailer != null || HasStandaloneTrailers(truck)) 
            {
                OptimizeTruckWheelColliders(truck);
            }
        }
    }

    private bool HasStandaloneTrailers(RCC_CarControllerV4 truck) 
    {
        // Check for standalone trailers connected to this truck
        StandaloneTrailerController[] trailers = FindObjectsOfType<StandaloneTrailerController>();
        
        foreach (StandaloneTrailerController trailer in trailers) 
        {
            if (trailer.isAttached) 
            {
                ConfigurableJoint joint = trailer.GetComponent<ConfigurableJoint>();
                if (joint && joint.connectedBody == truck.Rigid) 
                {
                    return true;
                }
            }
        }
        
        return false;
    }

    private void OptimizeTruckWheelColliders(RCC_CarControllerV4 truck) 
    {
        // Get all wheel colliders from the truck
        RCC_WheelCollider[] rccWheels = truck.AllWheelColliders;
        
        foreach (RCC_WheelCollider rccWheel in rccWheels) 
        {
            WheelCollider wheel = rccWheel.WheelCollider;
            
            // Apply extra grip for truck wheels when pulling trailers
            if (!originalForwardFrictions.ContainsKey(wheel)) 
            {
                originalForwardFrictions[wheel] = wheel.forwardFriction;
                originalSidewaysFrictions[wheel] = wheel.sidewaysFriction;
            }

            // Enhanced truck wheel friction
            WheelFrictionCurve forwardFriction = wheel.forwardFriction;
            forwardFriction.stiffness *= truckWheelGripMultiplier;
            forwardFriction.extremumValue = Mathf.Min(3f, forwardFriction.extremumValue * 1.5f);
            forwardFriction.asymptoteValue = Mathf.Min(2f, forwardFriction.asymptoteValue * 1.3f);
            wheel.forwardFriction = forwardFriction;

            WheelFrictionCurve sidewaysFriction = wheel.sidewaysFriction;
            sidewaysFriction.stiffness *= truckWheelGripMultiplier * 0.8f;
            sidewaysFriction.extremumValue = Mathf.Min(3f, sidewaysFriction.extremumValue * 1.4f);
            sidewaysFriction.asymptoteValue = Mathf.Min(2f, sidewaysFriction.asymptoteValue * 1.2f);
            wheel.sidewaysFriction = sidewaysFriction;

            // Increase wheel mass for better traction
            wheel.mass = wheelMassMultiplier * 1.5f;

            Debug.Log($"🚛 Enhanced truck wheel: {wheel.name} with {truckWheelGripMultiplier}x grip");
        }
    }

    [ContextMenu("Reset Physics")]
    public void ResetPhysics() 
    {
        Debug.Log("🔄 Resetting physics to original values...");
        
        // Reset wheel frictions
        foreach (var kvp in originalForwardFrictions) 
        {
            if (kvp.Key) 
            {
                kvp.Key.forwardFriction = kvp.Value;
            }
        }
        
        foreach (var kvp in originalSidewaysFrictions) 
        {
            if (kvp.Key) 
            {
                kvp.Key.sidewaysFriction = kvp.Value;
            }
        }

        optimizationActive = false;
        optimizedJoints = 0;
        optimizedWheels = 0;
        
        Debug.Log("✅ Physics reset complete!");
    }

    private void OnDrawGizmosSelected() 
    {
        if (optimizationActive) 
        {
            // Draw optimization status
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position + Vector3.up * 3f, 1f);
            
            // Draw connections to optimized joints
            Gizmos.color = Color.yellow;
            foreach (ConfigurableJoint joint in processedJoints) 
            {
                if (joint) 
                {
                    Gizmos.DrawLine(transform.position, joint.transform.position);
                }
            }
        }
    }
}
