# 🚛 Standalone Trailer System
**Develop by <PERSON>**

## Overview
A complete independent trailer physics system that doesn't require RCC (Realistic Car Controller). Perfect for creating trailer-to-trailer connections and custom vehicle setups.

## Key Features

### ✅ **Independent Operation**
- No dependency on RCC system
- Works with any vehicle or trailer
- Standalone physics simulation
- Custom stability control

### ✅ **Advanced Stability System**
- Speed-based friction adjustment
- Anti-sway technology
- Angular damping control
- Dynamic joint configuration

### ✅ **Auto-Attachment System**
- Automatic detection of nearby vehicles/trailers
- Configurable attachment distance
- Layer-based filtering
- Manual attach/detach controls

### ✅ **Professional Inspector**
- Organized parameter sections
- Real-time status monitoring
- Built-in help and recommendations
- Ali Taj branding

## Components

### 1. StandaloneTrailerController.cs
Main controller script with all physics and stability features.

### 2. StandaloneTrailerControllerEditor.cs
Custom inspector for easy configuration and monitoring.

### 3. TrailerSetupHelper.cs
Automatic setup utility for quick trailer configuration.

## Installation & Setup

### Method 1: Manual Setup
1. Add `StandaloneTrailerController` to your trailer GameObject
2. Add `Rigidbody` and `ConfigurableJoint` components
3. Configure wheel colliders and models in the inspector
4. Set center of mass transform
5. Adjust stability parameters

### Method 2: Automatic Setup
1. Add `StandaloneTrailerController` to your trailer GameObject
2. Add `TrailerSetupHelper` to the same GameObject
3. Click "Setup Trailer" in the context menu or let it auto-setup on start
4. Fine-tune parameters in the inspector

## Configuration Parameters

### 🎯 Stability Settings
- **Stability Multiplier** (0.5-2.0): Overall stability enhancement
  - Recommended: 1.3 for good balance
- **Speed Based Stiffness** (0.1-1.0): Friction increase with speed
  - Recommended: 0.7 for responsive control
- **Max Stability Speed** (0-120 km/h): Speed threshold for max stability
  - Recommended: 90 km/h for highway speeds
- **Angular Drag Multiplier** (0.1-3.0): Rotational damping
  - Recommended: 1.8 for stable turns

### 🔗 Connection Settings
- **Attachment Distance**: Auto-attachment detection range
- **Attachable Layer Mask**: Which layers can be attached to
- **Is Attached**: Current connection status (read-only)

### 🛑 Brake Settings
- **Brake When Detached**: Auto-brake when not connected
- **Brake Force**: Force applied when detached
- **Handbrake Force**: Force applied with handbrake input

### 🎮 Input Controls
- **Space**: Handbrake
- **T**: Detach trailer
- Customizable key bindings in inspector

## Usage Examples

### Trailer-to-Trailer Connection
```csharp
// Get trailer components
StandaloneTrailerController trailer1 = firstTrailer.GetComponent<StandaloneTrailerController>();
StandaloneTrailerController trailer2 = secondTrailer.GetComponent<StandaloneTrailerController>();

// Attach second trailer to first
trailer2.AttachToTarget(trailer1.GetComponent<Rigidbody>());
```

### Manual Attachment
```csharp
// Attach to any rigidbody
Rigidbody targetVehicle = someVehicle.GetComponent<Rigidbody>();
trailerController.AttachToTarget(targetVehicle);
```

### Detachment
```csharp
// Detach trailer
trailerController.DetachTrailer();
```

## Advanced Features

### Speed-Based Friction Control
The system automatically adjusts wheel friction based on current speed:
- Low speeds: Standard friction for maneuverability
- High speeds: Increased friction for stability
- Smooth transitions prevent sudden changes

### Anti-Sway Technology
Detects lateral movement and applies counter-forces:
- Monitors sideways velocity
- Applies proportional counter-force
- Prevents trailer swaying during turns

### Dynamic Joint Configuration
Joint parameters adjust based on speed:
- Higher damping at high speeds
- Increased maximum force for stability
- Maintains realistic movement limits

## Troubleshooting

### Common Issues

**Trailer is too unstable:**
- Increase Stability Multiplier to 1.5-2.0
- Increase Angular Drag Multiplier to 2.0-2.5
- Check center of mass position (should be low)

**Trailer won't attach:**
- Check Attachment Distance (increase if needed)
- Verify Attachable Layer Mask includes target objects
- Ensure target has Rigidbody component

**Poor performance:**
- Reduce number of wheel colliders if possible
- Optimize wheel friction calculations
- Use appropriate physics timestep

**Unrealistic physics:**
- Lower Stability Multiplier to 1.0-1.2
- Reduce Speed Based Stiffness to 0.4-0.6
- Adjust wheel collider settings

## Performance Optimization

### Best Practices
1. Use appropriate number of wheel colliders (4-6 typically)
2. Set reasonable update frequencies
3. Use layer masks to limit attachment detection
4. Optimize wheel visual updates when not visible

### Physics Settings
- Use ContinuousDynamic collision detection for stability
- Set appropriate mass values (2000-5000 kg for trailers)
- Balance spring and damper values for smooth suspension

## Compatibility

### Unity Versions
- Unity 2022.3 LTS and newer
- Unity 6 fully supported
- Uses modern physics properties (linearVelocity, angularDamping)

### Platform Support
- Windows ✅
- Mac ✅
- Linux ✅
- Mobile platforms ✅ (with performance considerations)

## Support & Updates

### Features Roadmap
- [ ] Hydraulic suspension simulation
- [ ] Advanced tire physics
- [ ] Multi-trailer chain support
- [ ] VR/AR compatibility
- [ ] Network multiplayer support

### Contact
For support, feature requests, or custom implementations:
**Develop by Ali Taj** - Enhanced Trailer Physics Specialist

---

*This standalone trailer system provides professional-grade physics simulation independent of any specific vehicle controller, making it perfect for custom implementations and trailer-to-trailer connections.*
