using UnityEngine;
using System.Collections;

/// <summary>
/// Standalone Trailer Controller - Independent of RCC system
/// Provides stability and physics control for trailer-to-trailer connections
/// Develop by <PERSON>
/// </summary>
[RequireComponent(typeof(Rigidbody))]
[RequireComponent(typeof(ConfigurableJoint))]
public class StandaloneTrailerController : MonoBehaviour 
{
    [Header("🚛 Standalone Trailer System - Develop by <PERSON> Taj")]
    [Space(10)]

    [Header("🎯 Stability Settings")]
    [Range(0.5f, 2f)] public float stabilityMultiplier = 1.3f;
    [Range(0.1f, 1f)] public float speedBasedStiffness = 0.7f;
    [Range(0f, 120f)] public float maxStabilitySpeed = 90f;
    [Range(0.1f, 3f)] public float angularDragMultiplier = 1.8f;

    [Header("🛞 Wheel Configuration")]
    public TrailerWheel[] trailerWheels;

    [Head<PERSON>("⚙️ Physics Settings")]
    public Transform centerOfMass;
    public GameObject supportLegs;
    
    [Header("🔗 Connection Settings")]
    public bool isAttached = false;
    public float attachmentDistance = 2.5f;
    public LayerMask attachableLayerMask = -1;

    [Header("🛑 Brake Settings")]
    public bool brakeWhenDetached = true;
    public float brakeForce = 8000f;
    public float handbrakeForce = 12000f;



    // Private variables
    private Rigidbody rigid;
    private ConfigurableJoint joint;
    private float originalAngularDrag;
    private bool isSleeping = false;
    private float timer = 0f;



    /// <summary>
    /// Trailer wheel class for independent wheel control
    /// </summary>
    [System.Serializable]
    public class TrailerWheel 
    {
        public WheelCollider wheelCollider;
        public Transform wheelModel;
        
        private WheelFrictionCurve originalForwardFriction;
        private WheelFrictionCurve originalSidewaysFriction;
        private bool originalFrictionsStored = false;

        public void Initialize() 
        {
            if (!originalFrictionsStored && wheelCollider) 
            {
                originalForwardFriction = wheelCollider.forwardFriction;
                originalSidewaysFriction = wheelCollider.sidewaysFriction;
                originalFrictionsStored = true;
            }
        }

        public void ApplyBrake(float force) 
        {
            if (wheelCollider) 
                wheelCollider.brakeTorque = force;
        }

        public void ApplySpeedBasedFriction(float speed, float stabilityMult, float speedStiffness, float maxSpeed) 
        {
            if (!originalFrictionsStored || !wheelCollider) return;

            float speedRatio = Mathf.Clamp01(speed / maxSpeed);
            float stabilityFactor = Mathf.Lerp(1f, stabilityMult, speedRatio * speedStiffness);

            WheelFrictionCurve forwardFriction = originalForwardFriction;
            WheelFrictionCurve sidewaysFriction = originalSidewaysFriction;

            // Enhanced friction for high-speed stability
            forwardFriction.stiffness = originalForwardFriction.stiffness * stabilityFactor;
            sidewaysFriction.stiffness = originalSidewaysFriction.stiffness * stabilityFactor;

            // Reduce slip for better control at high speeds
            sidewaysFriction.extremumSlip = originalSidewaysFriction.extremumSlip * (1f - speedRatio * 0.4f);
            sidewaysFriction.asymptoteSlip = originalSidewaysFriction.asymptoteSlip * (1f - speedRatio * 0.3f);

            wheelCollider.forwardFriction = forwardFriction;
            wheelCollider.sidewaysFriction = sidewaysFriction;
        }

        public void UpdateWheelVisual() 
        {
            if (!wheelCollider || !wheelModel) return;

            Vector3 position;
            Quaternion rotation;
            wheelCollider.GetWorldPose(out position, out rotation);
            wheelModel.SetPositionAndRotation(position, rotation);
        }
    }

    private void Start() 
    {
        rigid = GetComponent<Rigidbody>();
        joint = GetComponent<ConfigurableJoint>();
        
        // Store original physics values
        originalAngularDrag = rigid.angularDamping;
        
        // Initialize wheels
        foreach (TrailerWheel wheel in trailerWheels) 
        {
            wheel.Initialize();
        }

        // Set center of mass
        if (centerOfMass) 
        {
            rigid.centerOfMass = transform.InverseTransformPoint(centerOfMass.position);
        }

        // Configure joint for stability
        ConfigureJointForStability();

        // Check if already connected
        if (joint.connectedBody) 
        {
            isAttached = true;
            if (supportLegs) supportLegs.SetActive(false);
        }
        else 
        {
            isAttached = false;
            if (supportLegs) supportLegs.SetActive(true);
        }
    }

    private void Update()
    {
        // Check if sleeping
        if (rigid.linearVelocity.magnitude < 0.01f && Mathf.Abs(rigid.angularVelocity.magnitude) < 0.01f)
            isSleeping = true;
        else
            isSleeping = false;

        // Update timer
        if (timer > 0f)
            timer -= Time.deltaTime;
        timer = Mathf.Clamp01(timer);

        // Update wheel visuals
        if (!isSleeping) 
        {
            foreach (TrailerWheel wheel in trailerWheels) 
            {
                wheel.UpdateWheelVisual();
            }
        }

        // Try to attach to nearby trailers
        if (!isAttached) 
        {
            TryAutoAttach();
        }
    }

    private void FixedUpdate() 
    {
        // Apply stability control
        if (isAttached) 
        {
            ApplyStabilityControl();
        }

        // Handle wheel physics
        float currentSpeed = rigid.linearVelocity.magnitude * 3.6f;
        
        foreach (TrailerWheel wheel in trailerWheels) 
        {
            // Apply brakes
            float brakeAmount = 0f;

            if (!isAttached && brakeWhenDetached)
            {
                brakeAmount = brakeForce;
            }
            
            wheel.ApplyBrake(brakeAmount);

            // Apply speed-based friction for stability
            if (isAttached) 
            {
                wheel.ApplySpeedBasedFriction(currentSpeed, stabilityMultiplier, speedBasedStiffness, maxStabilitySpeed);
            }
        }
    }

    private void ApplyStabilityControl() 
    {
        float currentSpeed = rigid.linearVelocity.magnitude * 3.6f;
        float speedRatio = Mathf.Clamp01(currentSpeed / maxStabilitySpeed);
        
        // Apply speed-based angular damping
        float targetAngularDrag = originalAngularDrag + (angularDragMultiplier * speedRatio);
        rigid.angularDamping = Mathf.Lerp(rigid.angularDamping, targetAngularDrag, Time.fixedDeltaTime * 3f);

        // Anti-sway control for high-speed stability
        if (currentSpeed > 25f) 
        {
            Vector3 lateralVelocity = Vector3.Project(rigid.linearVelocity, transform.right);
            float swayForce = lateralVelocity.magnitude * speedRatio * stabilityMultiplier;
            
            if (swayForce > 0.1f) 
            {
                Vector3 antiSwayForce = -lateralVelocity.normalized * swayForce * 1200f;
                rigid.AddForce(antiSwayForce, ForceMode.Force);
            }

            // Stabilizing torque to prevent excessive rotation
            float angularVelY = rigid.angularVelocity.y;
            if (Mathf.Abs(angularVelY) > 0.3f) 
            {
                Vector3 stabilizingTorque = Vector3.up * (-angularVelY * speedRatio * 2500f);
                rigid.AddTorque(stabilizingTorque, ForceMode.Force);
            }
        }

        // Dynamic joint adjustment for high-speed turns
        if (joint && currentSpeed > 35f) 
        {
            var angularDrive = joint.angularYZDrive;
            float stabilityFactor = 1f + (speedRatio * 0.6f);
            angularDrive.positionDamper = 6000f * stabilityFactor;
            angularDrive.maximumForce = 12000f * stabilityFactor;
            joint.angularYZDrive = angularDrive;
        }
    }

    private void ConfigureJointForStability() 
    {
        if (!joint) return;

        // Enhanced joint configuration for better stability
        joint.xMotion = ConfigurableJointMotion.Locked;
        joint.yMotion = ConfigurableJointMotion.Locked;
        joint.zMotion = ConfigurableJointMotion.Locked;
        
        joint.angularXMotion = ConfigurableJointMotion.Limited;
        joint.angularYMotion = ConfigurableJointMotion.Limited;
        joint.angularZMotion = ConfigurableJointMotion.Limited;

        // Set angular limits for realistic movement
        var angularYLimit = joint.angularYLimit;
        angularYLimit.limit = 45f;
        joint.angularYLimit = angularYLimit;

        var angularZLimit = joint.angularZLimit;
        angularZLimit.limit = 8f;
        joint.angularZLimit = angularZLimit;

        // Configure drives for stability
        var angularDrive = joint.angularYZDrive;
        angularDrive.positionSpring = 2000f;
        angularDrive.positionDamper = 4000f;
        angularDrive.maximumForce = 8000f;
        joint.angularYZDrive = angularDrive;

        joint.configuredInWorldSpace = true;
    }

    private void TryAutoAttach() 
    {
        if (timer > 0f) return;

        Collider[] nearbyObjects = Physics.OverlapSphere(transform.position, attachmentDistance, attachableLayerMask);
        
        foreach (Collider col in nearbyObjects) 
        {
            if (col.gameObject == gameObject) continue;

            // Check for other trailers or vehicles
            StandaloneTrailerController otherTrailer = col.GetComponent<StandaloneTrailerController>();
            Rigidbody otherRigidbody = col.GetComponent<Rigidbody>();
            
            if (otherTrailer || otherRigidbody) 
            {
                AttachToTarget(otherRigidbody);
                break;
            }
        }
    }

    public void AttachToTarget(Rigidbody target) 
    {
        if (!target || timer > 0f) return;

        joint.connectedBody = target;
        isAttached = true;
        timer = 1f;

        // Reset physics
        rigid.angularDamping = originalAngularDrag;
        
        if (supportLegs) supportLegs.SetActive(false);

        Debug.Log($"Trailer attached to: {target.name}");
    }

    public void DetachTrailer() 
    {
        if (!isAttached || timer > 0f) return;

        joint.connectedBody = null;
        isAttached = false;
        timer = 1f;

        if (supportLegs) supportLegs.SetActive(true);

        Debug.Log("Trailer detached");
    }

    private void OnDrawGizmosSelected() 
    {
        // Draw attachment range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, attachmentDistance);
        
        // Draw center of mass
        if (centerOfMass) 
        {
            Gizmos.color = Color.red;
            Gizmos.DrawSphere(centerOfMass.position, 0.1f);
        }
    }
}
