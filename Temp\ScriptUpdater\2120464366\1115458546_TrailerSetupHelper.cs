using UnityEngine;

/// <summary>
/// Helper script to automatically setup standalone trailers
/// Develop by <PERSON>
/// </summary>
public class TrailerSetupHelper : MonoBehaviour 
{
    [Header("🔧 Trailer Setup Helper - Develop by <PERSON>")]
    [Space(10)]
    
    [Header("Auto Setup Options")]
    public bool autoSetupOnStart = true;
    public bool findWheelCollidersAutomatically = true;
    public bool findWheelModelsAutomatically = true;
    public bool createCenterOfMassIfMissing = true;
    public bool setupPhysicsAutomatically = true;

    [Header("Physics Configuration")]
    public float trailerMass = 3000f;
    public float trailerDrag = 0.1f;
    public float trailerAngularDrag = 5f;

    [Header("Wheel Settings")]
    public float wheelMass = 80f;
    public float suspensionSpring = 35000f;
    public float suspensionDamper = 4500f;
    public float suspensionDistance = 0.2f;

    private void Start() 
    {
        if (autoSetupOnStart) 
        {
            SetupTrailer();
        }
    }

    [ContextMenu("Setup Trailer")]
    public void SetupTrailer() 
    {
        StandaloneTrailerController trailerController = GetComponent<StandaloneTrailerController>();
        
        if (!trailerController) 
        {
            Debug.LogError("StandaloneTrailerController component not found! Please add it first.");
            return;
        }

        Debug.Log("🚛 Setting up Standalone Trailer...");

        // Setup physics
        if (setupPhysicsAutomatically) 
        {
            SetupPhysics();
        }

        // Setup wheels
        if (findWheelCollidersAutomatically) 
        {
            SetupWheels(trailerController);
        }

        // Setup center of mass
        if (createCenterOfMassIfMissing) 
        {
            SetupCenterOfMass(trailerController);
        }

        // Setup joint
        SetupJoint();

        Debug.Log("✅ Trailer setup completed successfully!");
    }

    private void SetupPhysics() 
    {
        Rigidbody rb = GetComponent<Rigidbody>();
        if (!rb) 
        {
            rb = gameObject.AddComponent<Rigidbody>();
        }

        rb.mass = trailerMass;
        rb.linearDamping = trailerDrag;
        rb.angularDamping = trailerAngularDrag;
        rb.interpolation = RigidbodyInterpolation.Interpolate;
        rb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;

        Debug.Log($"📦 Physics setup: Mass={trailerMass}, Drag={trailerDrag}, AngularDrag={trailerAngularDrag}");
    }

    private void SetupWheels(StandaloneTrailerController trailerController) 
    {
        WheelCollider[] wheelColliders = GetComponentsInChildren<WheelCollider>();
        
        if (wheelColliders.Length == 0) 
        {
            Debug.LogWarning("⚠️ No WheelColliders found in children!");
            return;
        }

        // Create wheel array
        StandaloneTrailerController.TrailerWheel[] wheels = new StandaloneTrailerController.TrailerWheel[wheelColliders.Length];

        for (int i = 0; i < wheelColliders.Length; i++) 
        {
            wheels[i] = new StandaloneTrailerController.TrailerWheel();
            wheels[i].wheelCollider = wheelColliders[i];

            // Configure wheel collider
            ConfigureWheelCollider(wheelColliders[i]);

            // Find wheel model
            if (findWheelModelsAutomatically) 
            {
                Transform wheelModel = FindWheelModel(wheelColliders[i]);
                wheels[i].wheelModel = wheelModel;
            }
        }

        // Assign to trailer controller using reflection
        var field = typeof(StandaloneTrailerController).GetField("trailerWheels");
        field.SetValue(trailerController, wheels);

        Debug.Log($"🛞 Setup {wheelColliders.Length} wheels with automatic configuration");
    }

    private void ConfigureWheelCollider(WheelCollider wheelCollider) 
    {
        // Configure suspension
        JointSpring suspensionSpring = wheelCollider.suspensionSpring;
        suspensionSpring.spring = this.suspensionSpring;
        suspensionSpring.damper = suspensionDamper;
        suspensionSpring.targetPosition = 0.5f;
        wheelCollider.suspensionSpring = suspensionSpring;

        wheelCollider.suspensionDistance = suspensionDistance;
        wheelCollider.mass = wheelMass;

        // Configure friction for better stability
        WheelFrictionCurve forwardFriction = wheelCollider.forwardFriction;
        forwardFriction.extremumSlip = 0.4f;
        forwardFriction.extremumValue = 1.0f;
        forwardFriction.asymptoteSlip = 0.8f;
        forwardFriction.asymptoteValue = 0.5f;
        forwardFriction.stiffness = 1.2f;
        wheelCollider.forwardFriction = forwardFriction;

        WheelFrictionCurve sidewaysFriction = wheelCollider.sidewaysFriction;
        sidewaysFriction.extremumSlip = 0.25f;
        sidewaysFriction.extremumValue = 1.0f;
        sidewaysFriction.asymptoteSlip = 0.5f;
        sidewaysFriction.asymptoteValue = 0.75f;
        sidewaysFriction.stiffness = 1.3f;
        wheelCollider.sidewaysFriction = sidewaysFriction;
    }

    private Transform FindWheelModel(WheelCollider wheelCollider) 
    {
        // Look for wheel model in the same parent or nearby
        Transform parent = wheelCollider.transform.parent;
        if (!parent) return null;

        // Common naming patterns for wheel models
        string[] searchPatterns = { 
            "Model", "Visual", "Mesh", "Wheel", 
            wheelCollider.name.Replace("Collider", "Model"),
            wheelCollider.name.Replace("Collider", "Visual"),
            wheelCollider.name.Replace("Collider", "Mesh")
        };

        foreach (string pattern in searchPatterns) 
        {
            Transform found = parent.Find(pattern);
            if (found) return found;
        }

        // Look in children
        foreach (Transform child in parent) 
        {
            if (child != wheelCollider.transform && child.name.ToLower().Contains("wheel")) 
            {
                return child;
            }
        }

        Debug.LogWarning($"⚠️ Could not find wheel model for {wheelCollider.name}");
        return null;
    }

    private void SetupCenterOfMass(StandaloneTrailerController trailerController) 
    {
        if (trailerController.centerOfMass == null) 
        {
            // Create center of mass object
            GameObject comObject = new GameObject("Center_Of_Mass");
            comObject.transform.SetParent(transform);
            comObject.transform.localPosition = new Vector3(0, -0.5f, 0); // Slightly below center
            comObject.transform.localRotation = Quaternion.identity;

            // Assign to trailer controller using reflection
            var field = typeof(StandaloneTrailerController).GetField("centerOfMass");
            field.SetValue(trailerController, comObject.transform);

            Debug.Log("📍 Created Center of Mass at position: " + comObject.transform.localPosition);
        }
    }

    private void SetupJoint() 
    {
        ConfigurableJoint joint = GetComponent<ConfigurableJoint>();
        if (!joint) 
        {
            joint = gameObject.AddComponent<ConfigurableJoint>();
        }

        // Configure for trailer connection
        joint.xMotion = ConfigurableJointMotion.Locked;
        joint.yMotion = ConfigurableJointMotion.Locked;
        joint.zMotion = ConfigurableJointMotion.Locked;

        joint.angularXMotion = ConfigurableJointMotion.Limited;
        joint.angularYMotion = ConfigurableJointMotion.Limited;
        joint.angularZMotion = ConfigurableJointMotion.Limited;

        // Set limits
        SoftJointLimit angularYLimit = joint.angularYLimit;
        angularYLimit.limit = 45f;
        joint.angularYLimit = angularYLimit;

        SoftJointLimit angularZLimit = joint.angularZLimit;
        angularZLimit.limit = 10f;
        joint.angularZLimit = angularZLimit;

        // Configure drives
        JointDrive angularDrive = joint.angularYZDrive;
        angularDrive.positionSpring = 2000f;
        angularDrive.positionDamper = 4000f;
        angularDrive.maximumForce = 8000f;
        joint.angularYZDrive = angularDrive;

        joint.configuredInWorldSpace = true;

        Debug.Log("🔗 ConfigurableJoint setup completed");
    }

    [ContextMenu("Reset Trailer Setup")]
    public void ResetTrailerSetup() 
    {
        StandaloneTrailerController trailerController = GetComponent<StandaloneTrailerController>();
        if (trailerController) 
        {
            // Reset wheel array
            var field = typeof(StandaloneTrailerController).GetField("trailerWheels");
            field.SetValue(trailerController, new StandaloneTrailerController.TrailerWheel[0]);

            Debug.Log("🔄 Trailer setup reset. Run Setup Trailer again to reconfigure.");
        }
    }

    private void OnDrawGizmosSelected() 
    {
        // Draw setup visualization
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireCube(transform.position, transform.localScale);
        
        // Draw center of mass if exists
        StandaloneTrailerController trailerController = GetComponent<StandaloneTrailerController>();
        if (trailerController && trailerController.centerOfMass) 
        {
            Gizmos.color = Color.red;
            Gizmos.DrawSphere(trailerController.centerOfMass.position, 0.15f);
        }
    }
}
