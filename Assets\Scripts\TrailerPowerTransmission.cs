using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Trailer Power Transmission - Enables trailers to pull other trailers
/// Develop by <PERSON> Taj
/// </summary>
public class TrailerPowerTransmission : MonoBehaviour 
{
    [Header("🔗 Trailer Power Transmission - Develop by <PERSON> Taj")]
    [Space(10)]

    [Header("⚡ Power Transmission Settings")]
    [Range(0.1f, 1f)] public float powerTransmissionEfficiency = 0.8f;
    [Range(100f, 5000f)] public float maxTransmissionForce = 2000f;
    [Range(0.1f, 2f)] public float forceMultiplier = 1.2f;
    [Range(1f, 10f)] public float transmissionRange = 5f;

    [Header("🚚 Trailer Detection")]
    [Range(0.5f, 5f)] public float detectionRadius = 2f;
    [Range(0.1f, 2f)] public float updateFrequency = 0.5f;
    public LayerMask trailerLayerMask = -1;

    [Header("🔧 Force Application")]
    [Range(0.1f, 5f)] public float forceApplicationHeight = 1f;
    [Range(0.1f, 2f)] public float forceDamping = 0.8f;
    [Range(10f, 1000f)] public float minimumForceThreshold = 50f;

    [Header("📊 Debug Information")]
    [SerializeField] private bool isReceivingPower = false;
    [SerializeField] private bool isTransmittingPower = false;
    [SerializeField] private float currentTransmittedForce = 0f;
    [SerializeField] private int connectedTrailerCount = 0;
    [SerializeField] private Vector3 lastAppliedForce = Vector3.zero;

    // Private variables
    private Rigidbody rigid;
    private ConfigurableJoint joint;
    private List<TrailerPowerTransmission> connectedTrailers = new List<TrailerPowerTransmission>();
    private TrailerPowerTransmission powerSource;
    private RCC_CarControllerV4 sourceTruck;
    private float lastUpdateTime = 0f;
    private Vector3 lastVelocity = Vector3.zero;
    private bool isInitialized = false;

    private void Start() 
    {
        rigid = GetComponent<Rigidbody>();
        joint = GetComponent<ConfigurableJoint>();
        
        // Delay initialization to ensure all objects are ready
        Invoke(nameof(Initialize), 1f);
        
        Debug.Log($"🔗 Trailer Power Transmission initialized on: {gameObject.name}");
    }

    private void Initialize() 
    {
        FindSourceTruck();
        DetectConnectedTrailers();
        isInitialized = true;
    }

    private void FixedUpdate() 
    {
        if (!isInitialized) return;

        // Update detection periodically
        if (Time.time - lastUpdateTime > updateFrequency) 
        {
            DetectConnectedTrailers();
            lastUpdateTime = Time.time;
        }

        // Apply power transmission
        ApplyPowerTransmission();
    }

    private void FindSourceTruck() 
    {
        // Find the truck that's pulling this trailer chain
        if (joint && joint.connectedBody) 
        {
            // Check if directly connected to truck
            sourceTruck = joint.connectedBody.GetComponent<RCC_CarControllerV4>();
            
            if (!sourceTruck) 
            {
                // Check if connected to another trailer that has a truck source
                TrailerPowerTransmission sourceTrailer = joint.connectedBody.GetComponent<TrailerPowerTransmission>();
                if (sourceTrailer) 
                {
                    powerSource = sourceTrailer;
                    sourceTruck = sourceTrailer.sourceTruck;
                }
            }
        }

        if (sourceTruck) 
        {
            Debug.Log($"🚛 Found source truck: {sourceTruck.name} for trailer: {gameObject.name}");
        }
    }

    private void DetectConnectedTrailers() 
    {
        connectedTrailers.Clear();
        connectedTrailerCount = 0;

        // Find trailers connected to this trailer
        Collider[] nearbyColliders = Physics.OverlapSphere(transform.position, detectionRadius, trailerLayerMask);
        
        foreach (Collider col in nearbyColliders) 
        {
            if (col.gameObject == gameObject) continue;

            TrailerPowerTransmission otherTrailer = col.GetComponent<TrailerPowerTransmission>();
            if (otherTrailer) 
            {
                // Check if this trailer is connected to us via joint
                ConfigurableJoint otherJoint = otherTrailer.GetComponent<ConfigurableJoint>();
                if (otherJoint && otherJoint.connectedBody == rigid) 
                {
                    connectedTrailers.Add(otherTrailer);
                    connectedTrailerCount++;
                    
                    // Set us as their power source
                    otherTrailer.powerSource = this;
                    otherTrailer.sourceTruck = sourceTruck;
                }
            }
        }

        isTransmittingPower = connectedTrailerCount > 0;
    }

    private void ApplyPowerTransmission() 
    {
        if (!sourceTruck) return;

        // Calculate the force we should receive/transmit
        Vector3 truckForce = CalculateTruckPullingForce();
        
        if (truckForce.magnitude > minimumForceThreshold) 
        {
            isReceivingPower = true;
            
            // Apply force to ourselves if we're not the first trailer
            if (powerSource != null) 
            {
                ApplyReceivedForce(truckForce);
            }
            
            // Transmit force to connected trailers
            if (connectedTrailers.Count > 0) 
            {
                TransmitForceToTrailers(truckForce);
            }
        }
        else 
        {
            isReceivingPower = false;
            currentTransmittedForce = 0f;
        }
    }

    private Vector3 CalculateTruckPullingForce() 
    {
        if (!sourceTruck) return Vector3.zero;

        // Calculate force based on truck's throttle and movement
        float throttleInput = sourceTruck.throttleInput;
        Vector3 truckVelocity = sourceTruck.Rigid.linearVelocity;
        Vector3 truckForward = sourceTruck.transform.forward;

        // Base force calculation
        float baseForce = sourceTruck.engineTorque * throttleInput * forceMultiplier;
        
        // Direction-based force (forward movement)
        Vector3 forceDirection = truckForward;
        
        // Adjust force based on truck's actual movement
        if (truckVelocity.magnitude > 0.1f) 
        {
            forceDirection = truckVelocity.normalized;
        }

        // Calculate final force
        Vector3 finalForce = forceDirection * baseForce;
        
        // Apply transmission efficiency loss
        float distanceFromTruck = GetDistanceFromTruck();
        float efficiencyLoss = Mathf.Clamp01(distanceFromTruck / transmissionRange);
        finalForce *= (powerTransmissionEfficiency * (1f - efficiencyLoss));

        // Clamp maximum force
        if (finalForce.magnitude > maxTransmissionForce) 
        {
            finalForce = finalForce.normalized * maxTransmissionForce;
        }

        return finalForce;
    }

    private float GetDistanceFromTruck() 
    {
        if (!sourceTruck) return 0f;
        
        return Vector3.Distance(transform.position, sourceTruck.transform.position);
    }

    private void ApplyReceivedForce(Vector3 force) 
    {
        if (!rigid) return;

        // Apply force at center of mass with slight upward offset
        Vector3 forcePosition = transform.position + Vector3.up * forceApplicationHeight;
        
        // Damp the force to prevent oscillations
        Vector3 dampedForce = force * forceDamping;
        
        // Apply the force
        rigid.AddForceAtPosition(dampedForce, forcePosition, ForceMode.Force);
        
        lastAppliedForce = dampedForce;
        currentTransmittedForce = dampedForce.magnitude;
        
        Debug.DrawRay(forcePosition, dampedForce.normalized * 2f, Color.green, 0.1f);
    }

    private void TransmitForceToTrailers(Vector3 originalForce) 
    {
        foreach (TrailerPowerTransmission connectedTrailer in connectedTrailers) 
        {
            if (connectedTrailer && connectedTrailer.rigid) 
            {
                // Calculate transmitted force with efficiency loss
                Vector3 transmittedForce = originalForce * powerTransmissionEfficiency;
                
                // Apply force to connected trailer
                connectedTrailer.ApplyReceivedForce(transmittedForce);
                
                Debug.Log($"🔗 Transmitting {transmittedForce.magnitude:F1}N force from {gameObject.name} to {connectedTrailer.gameObject.name}");
            }
        }
    }

    public void ForceRecalculation() 
    {
        FindSourceTruck();
        DetectConnectedTrailers();
        Debug.Log($"🔄 Force recalculation completed for: {gameObject.name}");
    }

    private void OnDrawGizmosSelected() 
    {
        // Draw detection radius
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRadius);
        
        // Draw transmission range
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(transform.position, transmissionRange);
        
        // Draw connections to other trailers
        if (connectedTrailers != null) 
        {
            Gizmos.color = Color.green;
            foreach (TrailerPowerTransmission trailer in connectedTrailers) 
            {
                if (trailer) 
                {
                    Gizmos.DrawLine(transform.position, trailer.transform.position);
                }
            }
        }
        
        // Draw connection to power source
        if (powerSource) 
        {
            Gizmos.color = Color.red;
            Gizmos.DrawLine(transform.position, powerSource.transform.position);
        }
        
        // Draw connection to source truck
        if (sourceTruck) 
        {
            Gizmos.color = Color.magenta;
            Gizmos.DrawLine(transform.position, sourceTruck.transform.position);
        }
        
        // Draw last applied force
        if (lastAppliedForce.magnitude > 0) 
        {
            Gizmos.color = Color.cyan;
            Vector3 forcePos = transform.position + Vector3.up * forceApplicationHeight;
            Gizmos.DrawRay(forcePos, lastAppliedForce.normalized * 3f);
        }
    }

    private void OnDrawGizmos() 
    {
        // Show power transmission status
        if (isReceivingPower) 
        {
            Gizmos.color = Color.green;
            Gizmos.DrawWireCube(transform.position + Vector3.up * 2f, Vector3.one * 0.5f);
        }
        
        if (isTransmittingPower) 
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawWireCube(transform.position + Vector3.up * 2.5f, Vector3.one * 0.3f);
        }
    }

    private void OnValidate() 
    {
        // Ensure reasonable values
        powerTransmissionEfficiency = Mathf.Clamp01(powerTransmissionEfficiency);
        maxTransmissionForce = Mathf.Max(100f, maxTransmissionForce);
        detectionRadius = Mathf.Max(0.5f, detectionRadius);
        transmissionRange = Mathf.Max(1f, transmissionRange);
    }
}
