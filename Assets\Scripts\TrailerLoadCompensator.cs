using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Trailer Load Compensator - Automatically adjusts truck power based on trailer load
/// Develop by <PERSON>
/// </summary>
[RequireComponent(typeof(RCC_CarControllerV4))]
public class TrailerLoadCompensator : MonoBehaviour 
{
    [Header("🚛 Trailer Load Compensator - Develop by <PERSON> Taj")]
    [Space(10)]

    [Header("⚡ Power Compensation Settings")]
    [Range(1f, 5f)] public float maxTorqueMultiplier = 2.5f;
    [Range(1f, 3f)] public float maxEngineRPMMultiplier = 1.5f;
    [Range(0.5f, 2f)] public float gearRatioMultiplier = 1.3f;
    [Range(1000f, 10000f)] public float baseTrailerMass = 3000f;

    [Header("🔧 Advanced Settings")]
    [Range(0.1f, 1f)] public float compensationSmoothness = 0.3f;
    [Range(0f, 2000f)] public float massThreshold = 1000f;
    [Range(1f, 10f)] public float maxCompensationFactor = 3f;

    [Header("📊 Debug Information")]
    [SerializeField] private float currentTotalMass;
    [SerializeField] private float currentCompensationFactor;
    [SerializeField] private int attachedTrailerCount;
    [SerializeField] private bool compensationActive;

    // Private variables
    private RCC_CarControllerV4 carController;
    private float originalMaxEngineTorque;
    private float originalMaxEngineRPM;
    private float[] originalGearRatios;
    private float targetCompensationFactor = 1f;
    private List<Rigidbody> connectedTrailers = new List<Rigidbody>();

    private void Start() 
    {
        carController = GetComponent<RCC_CarControllerV4>();
        
        // Store original values
        originalMaxEngineTorque = carController.maxEngineTorque;
        originalMaxEngineRPM = carController.maxEngineRPM;
        
        // Store original gear ratios
        if (carController.gears != null && carController.gears.Length > 0) 
        {
            originalGearRatios = new float[carController.gears.Length];
            for (int i = 0; i < carController.gears.Length; i++) 
            {
                originalGearRatios[i] = carController.gears[i].maxRatio;
            }
        }

        Debug.Log("🚛 Trailer Load Compensator initialized - Original Torque: " + originalMaxEngineTorque);
    }

    private void Update() 
    {
        // Update trailer detection
        DetectConnectedTrailers();
        
        // Calculate compensation factor
        CalculateCompensationFactor();
        
        // Apply compensation smoothly
        ApplyCompensation();
    }

    private void DetectConnectedTrailers() 
    {
        connectedTrailers.Clear();
        currentTotalMass = carController.Rigid.mass;
        attachedTrailerCount = 0;

        // Check for RCC trailers
        if (carController.attachedTrailer != null) 
        {
            AddTrailerToList(carController.attachedTrailer.GetComponent<Rigidbody>());
            
            // Check for trailer-to-trailer connections
            CheckTrailerChain(carController.attachedTrailer.gameObject);
        }

        // Check for standalone trailers
        CheckForStandaloneTrailers();
    }

    private void CheckTrailerChain(GameObject currentTrailer) 
    {
        // Look for ConfigurableJoints that might connect to other trailers
        ConfigurableJoint[] joints = currentTrailer.GetComponentsInChildren<ConfigurableJoint>();
        
        foreach (ConfigurableJoint joint in joints) 
        {
            if (joint.connectedBody != null && joint.connectedBody != carController.Rigid) 
            {
                // Check if this is another trailer
                StandaloneTrailerController standaloneTrailer = joint.connectedBody.GetComponent<StandaloneTrailerController>();
                RCC_TruckTrailer rccTrailer = joint.connectedBody.GetComponent<RCC_TruckTrailer>();
                
                if (standaloneTrailer || rccTrailer) 
                {
                    AddTrailerToList(joint.connectedBody);
                    
                    // Recursively check for more trailers
                    CheckTrailerChain(joint.connectedBody.gameObject);
                }
            }
        }
    }

    private void CheckForStandaloneTrailers() 
    {
        // Find all standalone trailers in the scene that might be connected
        StandaloneTrailerController[] allStandaloneTrailers = FindObjectsOfType<StandaloneTrailerController>();
        
        foreach (StandaloneTrailerController trailer in allStandaloneTrailers) 
        {
            if (trailer.isAttached) 
            {
                ConfigurableJoint joint = trailer.GetComponent<ConfigurableJoint>();
                if (joint && joint.connectedBody) 
                {
                    // Check if this trailer is connected to our truck or trailer chain
                    if (IsConnectedToOurChain(joint.connectedBody)) 
                    {
                        AddTrailerToList(trailer.GetComponent<Rigidbody>());
                    }
                }
            }
        }
    }

    private bool IsConnectedToOurChain(Rigidbody connectedBody) 
    {
        // Check if connected to our truck
        if (connectedBody == carController.Rigid) 
            return true;

        // Check if connected to any trailer in our chain
        foreach (Rigidbody trailer in connectedTrailers) 
        {
            if (connectedBody == trailer) 
                return true;
        }

        return false;
    }

    private void AddTrailerToList(Rigidbody trailerRigidbody) 
    {
        if (trailerRigidbody && !connectedTrailers.Contains(trailerRigidbody)) 
        {
            connectedTrailers.Add(trailerRigidbody);
            currentTotalMass += trailerRigidbody.mass;
            attachedTrailerCount++;
        }
    }

    private void CalculateCompensationFactor() 
    {
        if (attachedTrailerCount == 0) 
        {
            targetCompensationFactor = 1f;
            compensationActive = false;
            return;
        }

        compensationActive = true;

        // Calculate mass-based compensation
        float excessMass = currentTotalMass - carController.Rigid.mass - massThreshold;
        float massCompensation = 1f + (excessMass / baseTrailerMass);

        // Calculate trailer count-based compensation
        float countCompensation = 1f + (attachedTrailerCount * 0.4f);

        // Combine both factors
        targetCompensationFactor = Mathf.Max(massCompensation, countCompensation);
        targetCompensationFactor = Mathf.Clamp(targetCompensationFactor, 1f, maxCompensationFactor);

        Debug.Log($"🔧 Mass: {currentTotalMass:F0}kg, Trailers: {attachedTrailerCount}, Compensation: {targetCompensationFactor:F2}x");
    }

    private void ApplyCompensation() 
    {
        // Smooth transition to target compensation
        currentCompensationFactor = Mathf.Lerp(currentCompensationFactor, targetCompensationFactor, 
            Time.deltaTime / compensationSmoothness);

        if (compensationActive) 
        {
            // Apply torque compensation
            float newMaxTorque = originalMaxEngineTorque * Mathf.Min(currentCompensationFactor * maxTorqueMultiplier, 
                originalMaxEngineTorque * maxTorqueMultiplier);
            carController.maxEngineTorque = newMaxTorque;

            // Apply RPM compensation for better power delivery
            float newMaxRPM = originalMaxEngineRPM * Mathf.Min(currentCompensationFactor * maxEngineRPMMultiplier, 
                originalMaxEngineRPM * maxEngineRPMMultiplier);
            carController.maxEngineRPM = newMaxRPM;

            // Apply gear ratio compensation for better pulling power
            if (originalGearRatios != null && carController.gears != null) 
            {
                for (int i = 0; i < Mathf.Min(originalGearRatios.Length, carController.gears.Length); i++) 
                {
                    float gearCompensation = Mathf.Min(currentCompensationFactor * gearRatioMultiplier, 
                        originalGearRatios[i] * gearRatioMultiplier);
                    carController.gears[i].maxRatio = originalGearRatios[i] * gearCompensation;
                }
            }
        }
        else 
        {
            // Reset to original values when no trailers
            carController.maxEngineTorque = originalMaxEngineTorque;
            carController.maxEngineRPM = originalMaxEngineRPM;
            
            if (originalGearRatios != null && carController.gears != null) 
            {
                for (int i = 0; i < Mathf.Min(originalGearRatios.Length, carController.gears.Length); i++) 
                {
                    carController.gears[i].maxRatio = originalGearRatios[i];
                }
            }
        }
    }

    public void ForceRecalculation() 
    {
        DetectConnectedTrailers();
        CalculateCompensationFactor();
    }

    private void OnDrawGizmosSelected() 
    {
        if (compensationActive) 
        {
            // Draw compensation visualization
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position + Vector3.up * 2f, currentCompensationFactor * 0.5f);
            
            // Draw connections to trailers
            Gizmos.color = Color.yellow;
            foreach (Rigidbody trailer in connectedTrailers) 
            {
                if (trailer) 
                {
                    Gizmos.DrawLine(transform.position, trailer.transform.position);
                }
            }
        }
    }

    private void OnValidate() 
    {
        // Ensure values are within reasonable ranges
        maxTorqueMultiplier = Mathf.Clamp(maxTorqueMultiplier, 1f, 5f);
        maxEngineRPMMultiplier = Mathf.Clamp(maxEngineRPMMultiplier, 1f, 3f);
        gearRatioMultiplier = Mathf.Clamp(gearRatioMultiplier, 0.5f, 2f);
        maxCompensationFactor = Mathf.Clamp(maxCompensationFactor, 1f, 10f);
    }
}
