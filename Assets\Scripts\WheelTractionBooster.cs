using UnityEngine;

/// <summary>
/// Wheel Traction Booster - Dramatically improves wheel grip for heavy loads
/// Develop by <PERSON>
/// </summary>
[RequireComponent(typeof(RCC_CarControllerV4))]
public class WheelTractionBooster : MonoBehaviour 
{
    [Header("🛞 Wheel Traction Booster - Develop by <PERSON>")]
    [Space(10)]

    [Header("⚡ Traction Enhancement")]
    [Range(1f, 10f)] public float tractionMultiplier = 5f;
    [Range(1f, 5f)] public float heavyLoadMultiplier = 3f;
    [Range(0.01f, 0.5f)] public float slipReduction = 0.1f;
    [Range(1f, 5f)] public float stiffnessBoost = 3f;

    [Header("🔧 Advanced Settings")]
    [Range(50f, 500f)] public float wheelMassBoost = 200f;
    [Range(0.5f, 5f)] public float dampingBoost = 2f;
    [Range(20000f, 100000f)] public float suspensionSpringBoost = 60000f;
    [Range(2000f, 10000f)] public float suspensionDamperBoost = 4000f;

    [Header("📊 Status")]
    [SerializeField] private bool boostActive = false;
    [SerializeField] private float currentTotalMass = 0f;
    [SerializeField] private int connectedTrailers = 0;

    private RCC_CarControllerV4 carController;
    private WheelFrictionCurve[] originalForwardFrictions;
    private WheelFrictionCurve[] originalSidewaysFrictions;
    private float[] originalWheelMasses;
    private float[] originalWheelDamping;
    private JointSpring[] originalSuspensions;
    private bool originalValuesStored = false;

    private void Start() 
    {
        carController = GetComponent<RCC_CarControllerV4>();
        StoreOriginalValues();
        
        // Apply boost immediately if trailers are already attached
        Invoke(nameof(CheckAndApplyBoost), 1f);
    }

    private void Update() 
    {
        CheckAndApplyBoost();
    }

    private void StoreOriginalValues() 
    {
        if (originalValuesStored || !carController || carController.AllWheelColliders == null) 
            return;

        int wheelCount = carController.AllWheelColliders.Length;
        
        originalForwardFrictions = new WheelFrictionCurve[wheelCount];
        originalSidewaysFrictions = new WheelFrictionCurve[wheelCount];
        originalWheelMasses = new float[wheelCount];
        originalWheelDamping = new float[wheelCount];
        originalSuspensions = new JointSpring[wheelCount];

        for (int i = 0; i < wheelCount; i++) 
        {
            WheelCollider wheel = carController.AllWheelColliders[i].WheelCollider;
            
            originalForwardFrictions[i] = wheel.forwardFriction;
            originalSidewaysFrictions[i] = wheel.sidewaysFriction;
            originalWheelMasses[i] = wheel.mass;
            originalWheelDamping[i] = wheel.wheelDampingRate;
            originalSuspensions[i] = wheel.suspensionSpring;
        }

        originalValuesStored = true;
        Debug.Log("🛞 Original wheel values stored for traction booster");
    }

    private void CheckAndApplyBoost() 
    {
        if (!originalValuesStored) 
        {
            StoreOriginalValues();
            return;
        }

        // Calculate total mass and trailer count
        CalculateLoadInfo();

        // Determine if boost should be active
        bool shouldBoost = connectedTrailers > 0 || currentTotalMass > (carController.Rigid.mass + 1000f);

        if (shouldBoost && !boostActive) 
        {
            ApplyTractionBoost();
        }
        else if (!shouldBoost && boostActive) 
        {
            RemoveTractionBoost();
        }
    }

    private void CalculateLoadInfo() 
    {
        currentTotalMass = carController.Rigid.mass;
        connectedTrailers = 0;

        // Check RCC trailers
        if (carController.attachedTrailer != null) 
        {
            connectedTrailers++;
            currentTotalMass += carController.attachedTrailer.GetComponent<Rigidbody>().mass;
            
            // Check for trailer chains
            CheckTrailerChain(carController.attachedTrailer.gameObject);
        }

        // Check standalone trailers
        CheckStandaloneTrailers();
    }

    private void CheckTrailerChain(GameObject currentTrailer) 
    {
        ConfigurableJoint[] joints = currentTrailer.GetComponentsInChildren<ConfigurableJoint>();
        
        foreach (ConfigurableJoint joint in joints) 
        {
            if (joint.connectedBody != null && joint.connectedBody != carController.Rigid) 
            {
                RCC_TruckTrailer rccTrailer = joint.connectedBody.GetComponent<RCC_TruckTrailer>();
                StandaloneTrailerController standaloneTrailer = joint.connectedBody.GetComponent<StandaloneTrailerController>();
                
                if (rccTrailer || standaloneTrailer) 
                {
                    connectedTrailers++;
                    currentTotalMass += joint.connectedBody.mass;
                    CheckTrailerChain(joint.connectedBody.gameObject);
                }
            }
        }
    }

    private void CheckStandaloneTrailers() 
    {
        StandaloneTrailerController[] allTrailers = FindObjectsOfType<StandaloneTrailerController>();
        
        foreach (StandaloneTrailerController trailer in allTrailers) 
        {
            if (trailer.isAttached) 
            {
                ConfigurableJoint joint = trailer.GetComponent<ConfigurableJoint>();
                if (joint && joint.connectedBody == carController.Rigid) 
                {
                    connectedTrailers++;
                    currentTotalMass += trailer.GetComponent<Rigidbody>().mass;
                }
            }
        }
    }

    private void ApplyTractionBoost() 
    {
        boostActive = true;
        
        // Calculate boost factor based on load
        float loadFactor = Mathf.Clamp(currentTotalMass / carController.Rigid.mass, 1f, heavyLoadMultiplier);
        float finalTractionMultiplier = tractionMultiplier * loadFactor;

        for (int i = 0; i < carController.AllWheelColliders.Length; i++) 
        {
            WheelCollider wheel = carController.AllWheelColliders[i].WheelCollider;
            
            // Boost forward friction for pulling power
            WheelFrictionCurve forwardFriction = originalForwardFrictions[i];
            forwardFriction.stiffness *= finalTractionMultiplier * stiffnessBoost;
            forwardFriction.extremumValue = Mathf.Min(5f, forwardFriction.extremumValue * finalTractionMultiplier);
            forwardFriction.asymptoteValue = Mathf.Min(3f, forwardFriction.asymptoteValue * (finalTractionMultiplier * 0.8f));
            forwardFriction.extremumSlip = Mathf.Max(0.01f, forwardFriction.extremumSlip - slipReduction);
            forwardFriction.asymptoteSlip = Mathf.Max(0.05f, forwardFriction.asymptoteSlip - slipReduction);
            wheel.forwardFriction = forwardFriction;

            // Boost sideways friction for stability
            WheelFrictionCurve sidewaysFriction = originalSidewaysFrictions[i];
            sidewaysFriction.stiffness *= finalTractionMultiplier * stiffnessBoost * 0.9f;
            sidewaysFriction.extremumValue = Mathf.Min(4f, sidewaysFriction.extremumValue * (finalTractionMultiplier * 0.9f));
            sidewaysFriction.asymptoteValue = Mathf.Min(2.5f, sidewaysFriction.asymptoteValue * (finalTractionMultiplier * 0.7f));
            sidewaysFriction.extremumSlip = Mathf.Max(0.01f, sidewaysFriction.extremumSlip - slipReduction);
            sidewaysFriction.asymptoteSlip = Mathf.Max(0.05f, sidewaysFriction.asymptoteSlip - slipReduction);
            wheel.sidewaysFriction = sidewaysFriction;

            // Boost wheel mass for better ground contact
            wheel.mass = originalWheelMasses[i] + (wheelMassBoost * loadFactor);

            // Boost wheel damping for stability
            wheel.wheelDampingRate = originalWheelDamping[i] * dampingBoost;

            // Boost suspension for heavy loads
            JointSpring suspension = originalSuspensions[i];
            suspension.spring = Mathf.Max(suspension.spring, suspensionSpringBoost * loadFactor);
            suspension.damper = Mathf.Max(suspension.damper, suspensionDamperBoost * loadFactor);
            wheel.suspensionSpring = suspension;
        }

        Debug.Log($"🚛 Traction boost ACTIVATED! Load factor: {loadFactor:F2}x, Trailers: {connectedTrailers}, Mass: {currentTotalMass:F0}kg");
    }

    private void RemoveTractionBoost() 
    {
        boostActive = false;

        // Restore original values
        for (int i = 0; i < carController.AllWheelColliders.Length; i++) 
        {
            WheelCollider wheel = carController.AllWheelColliders[i].WheelCollider;
            
            wheel.forwardFriction = originalForwardFrictions[i];
            wheel.sidewaysFriction = originalSidewaysFrictions[i];
            wheel.mass = originalWheelMasses[i];
            wheel.wheelDampingRate = originalWheelDamping[i];
            wheel.suspensionSpring = originalSuspensions[i];
        }

        Debug.Log("🛞 Traction boost DEACTIVATED - Normal wheel settings restored");
    }

    [ContextMenu("Force Apply Boost")]
    public void ForceApplyBoost() 
    {
        ApplyTractionBoost();
    }

    [ContextMenu("Force Remove Boost")]
    public void ForceRemoveBoost() 
    {
        RemoveTractionBoost();
    }

    private void OnDrawGizmosSelected() 
    {
        if (boostActive) 
        {
            // Draw boost visualization
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position + Vector3.up * 2f, tractionMultiplier * 0.2f);
            
            // Draw wheel positions
            if (carController && carController.AllWheelColliders != null) 
            {
                Gizmos.color = Color.yellow;
                foreach (RCC_WheelCollider rccWheel in carController.AllWheelColliders) 
                {
                    if (rccWheel.WheelCollider) 
                    {
                        Gizmos.DrawWireSphere(rccWheel.WheelCollider.transform.position, 0.3f);
                    }
                }
            }
        }
    }

    private void OnValidate() 
    {
        // Ensure reasonable values
        tractionMultiplier = Mathf.Clamp(tractionMultiplier, 1f, 10f);
        heavyLoadMultiplier = Mathf.Clamp(heavyLoadMultiplier, 1f, 5f);
        stiffnessBoost = Mathf.Clamp(stiffnessBoost, 1f, 5f);
    }
}
